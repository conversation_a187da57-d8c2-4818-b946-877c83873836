<?php

namespace App\Livewire;
use Filament\Forms\Components\Select;
use App\Forms\Components\CustomMonacoEditor;
use App\Models\Config;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\CreateAction;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Livewire\Component;
use App\Constants\ConfigValueTypeEnum;

class ConfigManager extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public function table(Table $table): Table
    {
        return $table
            ->query(Config::query())
            ->columns([
                TextColumn::make('key')
                    ->label(__('Key'))
                    ->searchable()
                    ->sortable(),
                TextColumn::make('value')
                    ->label(__('Value'))
                    ->limit(50)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    })
                    ->searchable(),
                TextColumn::make('value_type')
                    ->label(__('Value Type'))
                    ->sortable(),
                IconColumn::make('deletable')
                    ->label(__('Deletable'))
                    ->boolean()
                    ->sortable(),
                TextColumn::make('created_at')
                    ->label(__('Created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->label(__('Updated'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                CreateAction::make()
                    ->form([
                        TextInput::make('key')
                            ->label(__('Key'))
                            ->required()
                            ->unique(Config::class, 'key'),
                        Select::make('value_type')
                            ->label(__('Value Type'))
                            ->options(ConfigValueTypeEnum::toArray())
                            ->default(ConfigValueTypeEnum::STRING->value)
                            ->required()
                            ->extraAttributes([
                                'x-on:change' => "
                                    const isHtml = \$event.target.value === '" . ConfigValueTypeEnum::HTML->value . "';
                                    window.dispatchEvent(new CustomEvent('toggle-monaco-preview', {
                                        detail: {
                                            monacoId: 'config-value-create',
                                            isDisplayed: isHtml
                                        }
                                    }));
                                "
                            ]),
                        CustomMonacoEditor::make('value')
                            ->label(__('Value'))
                            ->language('json')
                            ->theme('blackboard')
                            ->fontSize('14px')
                            ->height('300px')
                            ->monacoId('config-value-create')
                             ->enablePreview(
                               true // initial value since string is the default selected value_type
                            ),
                    ])
                    ->using(function (array $data): Config {
                        return Config::create($data);
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title(__('Config created'))
                            ->body(__('The config has been created successfully.'))
                    ),
            ])
            ->actions([
                EditAction::make()
                    ->form([
                        TextInput::make('key')
                            ->label(__('Key'))
                            ->required()
                            ->unique(Config::class, 'key', ignoreRecord: true),
                        Select::make('value_type')
                            ->label(__('Value Type'))
                            ->options(ConfigValueTypeEnum::toArray())
                            ->default(ConfigValueTypeEnum::STRING->value)
                            ->extraAttributes([
                                'x-on:change' => "
                                    const isHtml = \$event.target.value === '" . ConfigValueTypeEnum::HTML->value . "';
                                    window.dispatchEvent(new CustomEvent('toggle-monaco-preview', {
                                        detail: {
                                            monacoId: 'config-value-edit',
                                            isDisplayed: isHtml
                                        }
                                    }));
                                "
                            ])
                            ->required(),
                        CustomMonacoEditor::make('value')
                            ->label(__('Value'))
                            ->language(
                                fn (Config $record) => $record->value_type === ConfigValueTypeEnum::JSON->value ? 'json' : 'html'
                            )
                            ->theme('blackboard')
                            ->fontSize('14px')
                            ->height('300px')
                            ->monacoId('config-value-edit')
                            ->enablePreview(
                                fn (Config $record) => $record->value_type === ConfigValueTypeEnum::HTML->value
                            ),

                    ])
                    ->using(function (Config $record, array $data): Config {
                        $record->update($data);
                        return $record;
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title(__('Config updated'))
                            ->body(__('The config has been updated successfully.'))
                    ),
                DeleteAction::make()
                    ->visible(fn (Config $record): bool => $record->deletable)
                    ->requiresConfirmation()
                    ->successNotification(
                        Notification::make()
                            ->success()
                            ->title(__('Config deleted'))
                            ->body(__('The config has been deleted successfully.'))
                    ),
            ])
            ->bulkActions([
                //
            ])
            ->defaultSort('key')
            ->paginated([10, 25, 50, 100]);
    }

    public function render()
    {
        return view('livewire.config-manager');
    }
}
